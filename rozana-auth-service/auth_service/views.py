import os
import dotenv
import re
import json
from django.utils import timezone
from django.http import JsonResponse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt

from oauth2_provider.models import AccessToken
from firebase_admin import firestore, credentials, auth
import firebase_admin

dotenv.load_dotenv()

# --- Firebase Initialization ---
try:

    if not firebase_admin._apps:
        service_account_path = os.getenv('FIREBASE_SERVICE_ACCOUNT_KEY_PATH')
        if not service_account_path:
            raise ValueError("FIREBASE_SERVICE_ACCOUNT_KEY_PATH not set in .env")
        
        cred = credentials.Certificate(service_account_path)
        firebase_admin.initialize_app(cred)
except Exception as e:
    print(f"Failed to initialize Firebase: {e}")

def get_firestore_db():
    return firestore.client()

# --- Helper Functions ---
def _normalize_phone_number(phone_number: str) -> str:
    """Cleans and standardizes a phone number to E.164 format."""
    cleaned = re.sub(r'[^\d+]', '', phone_number)
    if not cleaned.startswith('+'):
        if len(cleaned) == 10:
            cleaned = '+91' + cleaned
        elif len(cleaned) == 11 and cleaned.startswith('0'):
            cleaned = '+91' + cleaned[1:]
        else:
            cleaned = '+' + cleaned
    return cleaned

# --- Base Authenticated View ---
@method_decorator(csrf_exempt, name='dispatch')
class AuthenticatedView(View):
    """
    A base view that handles OAuth2 token authentication.
    """
    def dispatch(self, request, *args, **kwargs):
        token = request.headers.get('Authorization', '').replace('Bearer ', '')
        if not token:
            # Fallback for testing or simple clients
            token = request.GET.get('token') or request.POST.get('token')

        is_valid, error = self._authenticate_token(token)
        if not is_valid:
            return JsonResponse({'error': error}, status=401)
        
        # Add user to request if you need it later
        # request.user = AccessToken.objects.get(token=token).user
        
        return super().dispatch(request, *args, **kwargs)

    def _authenticate_token(self, token: str):
        if not token:
            return False, "No token provided"
        try:
            access_token = AccessToken.objects.get(token=token)
            if access_token.is_valid() and access_token.expires > timezone.now():
                return True, None
            return False, "Token expired or invalid"
        except AccessToken.DoesNotExist:
            return False, "Unauthorized"
        except Exception as e:
            return False, str(e)

# --- API Views ---
class CheckTokenView(AuthenticatedView):
    """
    Validate an access token via API. Inherits authentication.
    """
    def get(self, request):
        return JsonResponse({'valid': True})

    def post(self, request):
        return JsonResponse({'valid': True})

class CheckNumberView(AuthenticatedView):
    """
    Checks if a given phone number exists as a document ID in Firestore.
    """
    def get(self, request):
        number = request.GET.get('number')
        if not number:
            return JsonResponse({"error": "Number parameter is required"}, status=400)

        number_str = str(number).strip()
        if not number_str.startswith('+'):
            number_str = '+' + number_str

        try:
            db = get_firestore_db()
            doc_ref = db.collection("pos_users").document(number_str)
            return JsonResponse({"number_exists": doc_ref.get().exists})
        except Exception as e:
            return JsonResponse({"error": f"Error checking number in Firestore: {e}"}, status=500)


class UserCreateView(AuthenticatedView):
    """
    Searches for a user in Firebase Auth by phone number.
    """
    def get(self, request):
        phone_number = request.GET.get('phone_number')
        if not phone_number:
            return JsonResponse({"error": "phone_number parameter is required"}, status=400)

        normalized_phone = _normalize_phone_number(phone_number)

        try:
            user = auth.get_user_by_phone_number(normalized_phone)
            return JsonResponse({
                "success": True,
                "customer_id": user.uid,
                "customer_name": user.display_name or "Customer",
                "phone_number": user.phone_number,
            })
        except auth.UserNotFoundError:
            return JsonResponse({"success": False, "error": "User not found"}, status=404)
        except Exception as e:
            return JsonResponse({"success": False, "error": str(e)}, status=500)
    """
    Creates a new user in Firebase Auth if they don't already exist.
    """
    def post(self, request):
        try:
            data = json.loads(request.body)
            phone_number = data.get('phone_number')
            username = data.get('username')
            if not phone_number:
                return JsonResponse({"error": "phone_number is required"}, status=400)
        except json.JSONDecodeError:
            return JsonResponse({"error": "Invalid JSON"}, status=400)

        normalized_phone = _normalize_phone_number(phone_number)

        try:
            # Check if user exists first
            try:
                user = auth.get_user_by_phone_number(normalized_phone)
                return JsonResponse({
                    "success": True,
                    "created": False,
                    "customer_id": user.uid,
                    "customer_name": user.display_name or "Customer",
                    "phone_number": user.phone_number,
                })
            except auth.UserNotFoundError:
                # User does not exist, so create them
                new_user = auth.create_user(
                    phone_number=normalized_phone,
                    display_name=username or "Customer"
                )
                return JsonResponse({
                    "success": True,
                    "created": True,
                    "customer_id": new_user.uid,
                    "customer_name": new_user.display_name,
                    "phone_number": new_user.phone_number,
                }, status=201)
        except Exception as e:
            return JsonResponse({"success": False, "error": str(e)}, status=500)